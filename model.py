12# -*- encoding: utf-8 -*-
import os
import logging
import json
from pathlib import Path
from afts import Afts
from threading import Thread
import time
# 统一继承MayaBaseHandler，可以自动获取组件面板的模型文件
from aistudio_serving.hanlder.pymps_handler import MayaBaseHandler
# 组件使用文档，详见 https://yuque.antfin-inc.com/aii/aistudio/nkyse5
# python lib 依赖写入 requirement.txt 
from PIL import Image

from io import BytesIO
from io_utils import load_image, get_url_from_afts
from api_services import get_subject_analysis, enhance_image_quality

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        # logging.FileHandler("cover_enhance.log")
    ]
)
logger = logging.getLogger("CoverEnhance")



# 用户自定义代码处理类
class UserHandler(MayaBaseHandler):
# class UserHandler:
    """
     model_dir: model.py 文件所在目录
    """
    def __init__(self, model_dir):
        # 父类初始化
        super(UserHandler, self).__init__(model_dir)
        resource_path = Path(self.resource_path)
        import sys
        sys.path.append(str(resource_path))

    def predict_np(self, features, trace_id):
        resultCode = 0       # 0表示成功，其它为失败
        errorMessage = "ok"  # errorMessage为predict函数对外透出的信息
        result_map = {
            "output": json.dumps({}, ensure_ascii=False)
        }

        start = time.time()
        logger.info("trace_id: " + str(trace_id) + ", Input Features:\n" + str(features))    

        try:
            data = json.loads(features.get("data").decode())
            content_id = data['contentId']
            logger.info(f"{content_id}: input: {features}")
            logger.info(f"content id: {content_id}\t标题: {data['title']}")

            # img = load_image(data['cover_id'])
            # img.save('tests/cover.png')
            if data['cover_url'] == '':
                data['cover_url'] = get_url_from_afts(data['cover_id'])

            # 初始化logs字典收集耗时信息
            logs = {}

            # 获取分析结果
            subject_analysis_start = time.time()
            subject_analysis_result = get_subject_analysis(data)
            subject_analysis_time = time.time() - subject_analysis_start
            logs['get_subject_analysis_time'] = round(subject_analysis_time, 3)
            logger.info(f"get_subject_analysis耗时: {subject_analysis_time:.3f}秒")

            result = {
                'new_cover_id': '',
                'enhanced': False,
                'enhance_list': [],
                'logs': logs,
            }

            if subject_analysis_result:
                subject_analysis = json.loads(subject_analysis_result)
                logger.info(f"subject analysis: {subject_analysis}")

                if 'error' in subject_analysis:
                    logs['subject_analysis_error'] = subject_analysis['error']
                    result = {
                        'new_cover_id': '',
                        'enhanced': False,
                        'enhance_list': [],
                        'logs': logs,
                    }

                if 'main_recognition' in subject_analysis and subject_analysis['main_recognition'] == 'food':
                    logger.info("main recognition: food, calling image quality enhancement")

                    # 调用画质增强模块
                    enhance_start = time.time()
                    enhanced_result = enhance_image_quality(data['cover_url'], data['cover_id'], data)
                    enhance_time = time.time() - enhance_start
                    logs['enhance_image_quality_time'] = round(enhance_time, 3)
                    logger.info(f"enhance_image_quality耗时: {enhance_time:.3f}秒")

                    if 'error' in enhanced_result:
                        logs['enhance_image_quality_error'] = enhanced_result['error']
                        result = {
                            'new_cover_id': '',
                            'enhanced': False,
                            'enhance_list': [],
                            'logs': logs,
                        }
                    else:
                        enhanced_cover_id = enhanced_result['cover_id']
                        enhanced_cover_url = enhanced_result['cover_url']
                        result = {
                            'new_cover_id': enhanced_cover_id,
                            'new_cover_url': enhanced_cover_url,
                            'enhanced': True,
                            'enhance_list': ['food'],
                            'logs': logs,
                        }
                        logger.info(f"Image quality enhancement successful: {enhanced_cover_id}")
            else:
                logger.warning("Subject analysis failed, using original image")

            result_map = {
                "output": json.dumps(result, ensure_ascii=False)
            }

        except Exception as e:
            resultCode=1
            errorMessage= "wrong_input"
            logger.info("trace_id: " + str(trace_id) + f", {e}")
            # 在异常情况下也返回包含logs的结果
            error_result = {
                'new_cover_id': '',
                'enhanced': False,
                'enhance_list': [],
                'logs': {'error': str(e)}
            }
            result_map = {
                "output": json.dumps(error_result, ensure_ascii=False)
            }

        logger.info(f"trace_id: {trace_id}\ttime: {time.time() - start}")

        return (resultCode, errorMessage, result_map)


def run_demo():
    # 使用绝对路径初始化
    model = UserHandler(os.getcwd())
    data_example = {
        'cover_id': 'A*zbI-S4hAlU8AAAAAevAAAAgAerZ9AQ', # afts id
        'cover_url': '',
        'title': '芋泥的受众群体到底是谁',
        'cat': '美食',
        'video_name': '',
        'video_id': '',
        'contentId': '20250724OB020010033349094691',
        'opt_path': 'super', # one of ['super', 'full', 'full_m']
        'logs': {},
        'version': 1, 
        'usePost': False,
    }
    print(json.dumps(data_example, ensure_ascii=False))
    res = model.predict_np({
            'data': json.dumps(data_example, ensure_ascii=False).encode()
        }, "test_trace_id")[-1]
    print(res)


# 用于调试UserHandler类的功能
if __name__ == "__main__":
    run_demo()