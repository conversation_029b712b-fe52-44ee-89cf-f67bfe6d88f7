# MayaClient 调用方式说明

## 概述

现在支持两种调用方式：
1. **MayaClient 调用**（推荐）：使用 rserving_client 进行调用
2. **POST 调用**（回退）：使用传统的 HTTP POST 请求

## 使用方法

### 1. MayaClient 调用方式

默认情况下，系统会优先使用 MayaClient 调用方式。只需要在数据中设置以下参数：

```python
data = {
    'cover_id': 'your_cover_id',
    'cover_url': 'your_cover_url',
    'title': '标题',
    'cat': '分类',
    'contentId': 'content_id',
    # ... 其他必要参数
    
    # MayaClient 相关参数（可选）
    'usePost': False,      # 默认为 False，使用 MayaClient
    'timeout': 60000,      # 可选，超时时间（毫秒），默认 60 秒
}
```

### 2. POST 回退方式

如果需要强制使用 POST 方式，或者 MayaClient 不可用时自动回退：

```python
data = {
    'cover_id': 'your_cover_id',
    'cover_url': 'your_cover_url',
    'title': '标题',
    'cat': '分类',
    'contentId': 'content_id',
    # ... 其他必要参数
    
    # 强制使用 POST 方式
    'usePost': True,
}
```

## 技术实现

### 服务配置

系统支持两个不同的服务：

1. **主体分析服务**
   - scene_name: `"main_recognition_vlm"`
   - tensor_key: `"input_args"`
   - tensor_values: `[json.dumps({"cover_url": "...", "media_type": "video"})]`

2. **画质增强服务**
   - scene_name: `"main_deeplpf"`
   - tensor_key: `"input"`
   - tensor_values: `[json.dumps({"cover_url": "...", "cover_id": "..."})]`

### 返回结果

- **MayaClient 调用**：返回 `res.items[0].attributes`
- **POST 调用**：返回 `res['resultMap']`

两种方式的返回结果格式相同，系统会自动处理差异。

### 自动回退机制

1. 如果 `rserving_client` 库不可用，自动使用 POST 方式
2. 如果 MayaClient 调用失败，自动回退到 POST 方式
3. 如果设置 `usePost=True`，直接使用 POST 方式

## 测试

运行测试脚本验证功能：

```bash
python test_maya_client.py
```

测试脚本会分别测试：
1. MayaClient 调用方式
2. POST 回退方式

## 日志

系统会记录详细的调用日志，包括：
- 使用的调用方式（MayaClient 或 POST）
- 调用耗时
- 错误信息（如果有）
- 回退信息（如果发生回退）

## 注意事项

1. 确保 `rserving_client` 库已正确安装和配置
2. MayaClient 的超时时间以毫秒为单位
3. 系统会自动处理 MayaClient 不可用的情况
4. 建议在生产环境中使用 MayaClient 方式以获得更好的性能
