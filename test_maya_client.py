#!/usr/bin/env python3
# -*- encoding: utf-8 -*-

"""
测试 MayaClient 调用方式的脚本
"""

import json
import os
from model import UserHand<PERSON>

def test_maya_client():
    """测试 MayaClient 调用方式"""
    print("=== 测试 MayaClient 调用方式 ===")
    
    # 初始化模型
    model = UserHandler(os.getcwd())
    
    # 测试数据 - 使用 MayaClient
    data_maya = {
        'cover_id': 'A*zbI-S4hAlU8AAAAAevAAAAgAerZ9AQ',
        'cover_url': '',
        'title': '芋泥的受众群体到底是谁',
        'cat': '美食',
        'video_name': '',
        'video_id': '',
        'contentId': '20250724OB020010033349094691',
        'opt_path': 'super',
        'logs': {},
        'version': 1,
        'usePost': False,  # 使用 MayaClient
        'timeout': 30000   # 可选的超时设置
    }
    
    print("测试数据 (MayaClient):")
    print(json.dumps(data_maya, ensure_ascii=False, indent=2))
    
    try:
        # 调用模型
        result_code, error_message, result_map = model.predict_np({
            'data': json.dumps(data_maya, ensure_ascii=False).encode()
        }, "test_maya_client")
        
        print(f"\n结果码: {result_code}")
        print(f"错误信息: {error_message}")
        print("结果:")
        print(json.dumps(json.loads(result_map['output']), ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"测试失败: {e}")


def test_post_fallback():
    """测试 POST 回退方式"""
    print("\n=== 测试 POST 回退方式 ===")
    
    # 初始化模型
    model = UserHandler(os.getcwd())
    
    # 测试数据 - 强制使用 POST
    data_post = {
        'cover_id': 'A*zbI-S4hAlU8AAAAAevAAAAgAerZ9AQ',
        'cover_url': '',
        'title': '芋泥的受众群体到底是谁',
        'cat': '美食',
        'video_name': '',
        'video_id': '',
        'contentId': '20250724OB020010033349094691',
        'opt_path': 'super',
        'logs': {},
        'version': 1,
        'usePost': True,  # 强制使用 POST
    }
    
    print("测试数据 (POST):")
    print(json.dumps(data_post, ensure_ascii=False, indent=2))
    
    try:
        # 调用模型
        result_code, error_message, result_map = model.predict_np({
            'data': json.dumps(data_post, ensure_ascii=False).encode()
        }, "test_post_fallback")
        
        print(f"\n结果码: {result_code}")
        print(f"错误信息: {error_message}")
        print("结果:")
        print(json.dumps(json.loads(result_map['output']), ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_maya_client()
    test_post_fallback()
