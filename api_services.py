# -*- encoding: utf-8 -*-
import json
import logging
import requests

# Configure logging
logger = logging.getLogger("CoverEnhance")

def get_subject_analysis(data):
    """
    获取主体分析结果
    """
    url = "https://paiplusinference.alipay.com/inference/ccb90aaa457e5bb2_main_recognition_vlm/v1"
    body = {
        "features": {},
        "tensorFeatures": {
            "input_args": {
                "shapes": [1],
                "stringValues": [
                    "{\"cover_url\":\"https://mass.alipay.com/afts/file/MVYhSqfz1G4AAAAAgDAAAAgAennEAQBr?bizType=video_generation&token=trVYq6JrCGMCnthd13sCiZSF64QOUHc4ZfTYggxNhN0DAAAAZAAAxHloqGiC\",\"aiEffectList\":[{\"aftId\":\"dVWDTIVZ9tEAAAAAfUAAAAgAoBT3AABr\",\"effectType\":\"ai_stylize_GhibliStyle\",\"pageNo\":9,\"uuid\":\"1D9D67C5-53E3-4D96-BAFC-84001A9D2573\"}],\"generateId\":\"AIF2025080610523015618702814908\",\"componentName\":\"MayaCallbackComponent\"}"
                ]
            }
        }
    }
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    try:
        # 更新请求体中的cover_url
        json_data = json.loads(body['tensorFeatures']['input_args']['stringValues'][0])
        json_data['cover_url'] = data['cover_url']
        body['tensorFeatures']['input_args']['stringValues'][0] = json.dumps(json_data)
        
        # 发送请求
        response = requests.post(url=url, json=body, headers=headers)
        res = response.json()
        logger.info(f"Subject analysis response: {res}")
        
        # 检查API调用是否成功
        if res.get('success') and 'resultMap' in res and 'output_args' in res['resultMap']:
            output_args = res['resultMap']['output_args']
            logger.info(f"Subject analysis successful: {output_args}")
            return output_args
        else:
            logger.warning(f"Subject analysis API call failed: {res.get('errorMessage', 'Unknown error')}")
            return {"error": res.get('errorMessage', 'Unknown error')}
            
    except Exception as e:
        logger.error(f"Unexpected error in subject analysis: {e}")
        return {"error": str(e)}


def enhance_image_quality(cover_url, cover_id):
    """
    调用画质增强模块
    """
    url = "https://paiplusinference.alipay.com/inference/c679eb929e2cdd91_main_deeplpf/v1"
    body = {
        "features": {},
        "tensorFeatures": {
            "input": {
                "shapes": [1],
                "stringValues": [json.dumps({
                    "cover_url": cover_url,
                    "cover_id": cover_id
                })]
            }
        }
    }
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    try:
        response = requests.post(url=url, json=body, headers=headers)
        res = response.json()
        logger.info(f"Image quality enhancement response: {res}")

        # 检查API调用是否成功
        if res.get('success') and 'resultMap' in res and 'output' in res['resultMap']:
            output_str = res['resultMap']['output']
            output_data = json.loads(output_str)
            logger.info(f"Parsed output data: {output_data}")

            # 从嵌套的output中提取增强后的图片信息
            if 'output' in output_data:
                enhanced_output = output_data['output']
                return enhanced_output
            else:
                logger.warning("No 'output' field in enhancement response data")
                return {"error": "No 'output' field in enhancement response data"}
        else:
            logger.warning(f"Enhancement API call failed: {res.get('errorMessage', 'Unknown error')}")
            return {"error": res.get('errorMessage', 'Unknown error')}

    except Exception as e:
        logger.error(f"Unexpected error in image quality enhancement: {e}")
        return {"error": str(e)}
