# -*- encoding: utf-8 -*-
import json
import logging
import requests

# Configure logging
logger = logging.getLogger("CoverEnhance")

# MayaClient imports
try:
    from rserving_client.rserving_client import (
        ArksConfig,
        ArksRequest,
        MayaClient
    )
    MAYA_CLIENT_AVAILABLE = True
except ImportError:
    logger.warning("MayaClient not available, falling back to POST requests")
    MAYA_CLIENT_AVAILABLE = False


def call_maya_client(scene_name, tensor_key, tensor_values, timeout=30000):
    """
    使用 MayaClient 调用服务

    Args:
        scene_name: 场景名称，如 "main_recognition_vlm"
        tensor_key: tensor 键名，如 "input_args"
        tensor_values: tensor 值列表
        timeout: 超时时间，默认60秒

    Returns:
        调用结果，成功时返回 res.items[0].attributes，失败时返回包含 error 的字典
    """
    if not MAYA_CLIENT_AVAILABLE:
        return {"error": "Maya<PERSON>lient not available"}

    try:
        # 获取 MayaClient 实例
        client = MayaClient.get_instance()

        # 创建请求
        request = ArksRequest()
        request.request_timeout = timeout
        request.read_timeout = timeout
        request.connect_timeout = timeout

        request.scene_name = scene_name
        request.chain_name = "v1"
        request.session_id = "traceId"
        request.set_lbconfig({"arks.antvip.arrow.cross.city": "true"})
        request.set_item_num(1)

        # 添加 item 和 tensor
        item_id = "testId"
        item = request.add_item(item_id)
        shapes = [1]
        item.add_tensor_feature(tensor_key, shapes, tensor_values)

        # 调用服务
        res = client.call(request)

        if res.success:
            logger.info("MayaClient request success")
            return res.items[0].attributes
        else:
            logger.error(f"MayaClient request failed: {res.error_msg}")
            return {"error": res.error_msg}

    except Exception as e:
        logger.error(f"MayaClient call exception: {e}")
        return {"error": str(e)}

def get_subject_analysis(data):
    """
    获取主体分析结果
    支持 MayaClient 和 POST 两种调用方式
    """
    use_post = data.get('usePost', False)

    if not use_post and MAYA_CLIENT_AVAILABLE:
        # 使用 MayaClient 调用
        logger.info("Using MayaClient for subject analysis")

        # 构造 tensor_values
        input_data = {
            "cover_url": data['cover_url'],
            "media_type": "video"
        }
        tensor_values = [json.dumps(input_data)]

        # 调用 MayaClient
        timeout = data.get('timeout', 30000)
        result = call_maya_client(
            scene_name="main_recognition_vlm",
            tensor_key="input_args",
            tensor_values=tensor_values,
            timeout=timeout
        )

        if 'error' not in result:
            # MayaClient 成功，返回结果
            if 'output_args' in result:
                logger.info(f"Subject analysis successful via MayaClient: {result['output_args']}")
                return result['output_args']
            else:
                logger.warning("No output_args in MayaClient response")
                return {"error": "No output_args in response"}
        else:
            logger.warning(f"MayaClient failed, falling back to POST: {result['error']}")
            # MayaClient 失败，回退到 POST 方式

    # 使用 POST 方式调用
    logger.info("Using POST request for subject analysis")

    url = "https://paiplusinference.alipay.com/inference/ccb90aaa457e5bb2_main_recognition_vlm/v1"
    body = {
        "features": {},
        "tensorFeatures": {
            "input_args": {
                "shapes": [1],
                "stringValues": [
                    "{\"cover_url\":\"https://mass.alipay.com/afts/file/MVYhSqfz1G4AAAAAgDAAAAgAennEAQBr?bizType=video_generation&token=trVYq6JrCGMCnthd13sCiZSF64QOUHc4ZfTYggxNhN0DAAAAZAAAxHloqGiC\",\"aiEffectList\":[{\"aftId\":\"dVWDTIVZ9tEAAAAAfUAAAAgAoBT3AABr\",\"effectType\":\"ai_stylize_GhibliStyle\",\"pageNo\":9,\"uuid\":\"1D9D67C5-53E3-4D96-BAFC-84001A9D2573\"}],\"generateId\":\"AIF2025080610523015618702814908\",\"componentName\":\"MayaCallbackComponent\"}"
                ]
            }
        }
    }
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    try:
        # 更新请求体中的cover_url
        json_data = json.loads(body['tensorFeatures']['input_args']['stringValues'][0])
        json_data['cover_url'] = data['cover_url']
        body['tensorFeatures']['input_args']['stringValues'][0] = json.dumps(json_data)

        # 发送请求
        response = requests.post(url=url, json=body, headers=headers)
        res = response.json()
        logger.info(f"Subject analysis response: {res}")

        # 检查API调用是否成功
        if res.get('success') and 'resultMap' in res and 'output_args' in res['resultMap']:
            output_args = res['resultMap']['output_args']
            logger.info(f"Subject analysis successful: {output_args}")
            return output_args
        else:
            logger.warning(f"Subject analysis API call failed: {res.get('errorMessage', 'Unknown error')}")
            return {"error": res.get('errorMessage', 'Unknown error')}

    except Exception as e:
        logger.error(f"Unexpected error in subject analysis: {e}")
        return {"error": str(e)}


def enhance_image_quality(cover_url, cover_id, data=None):
    """
    调用画质增强模块
    支持 MayaClient 和 POST 两种调用方式
    """
    use_post = data.get('usePost', False) if data else False

    if not use_post and MAYA_CLIENT_AVAILABLE:
        # 使用 MayaClient 调用
        logger.info("Using MayaClient for image quality enhancement")

        # 构造 tensor_values
        input_data = {
            "cover_url": cover_url,
            "cover_id": cover_id
        }
        tensor_values = [json.dumps(input_data)]

        # 调用 MayaClient
        timeout = data.get('timeout', 30000) if data else 30000
        result = call_maya_client(
            scene_name="main_deeplpf",
            tensor_key="input",
            tensor_values=tensor_values,
            timeout=timeout
        )

        if 'error' not in result:
            # MayaClient 成功，返回结果
            if 'output' in result:
                output_str = result['output']
                try:
                    output_data = json.loads(output_str)
                    logger.info(f"Parsed output data: {output_data}")

                    # 从嵌套的output中提取增强后的图片信息
                    if 'output' in output_data:
                        enhanced_output = output_data['output']
                        return enhanced_output
                    else:
                        logger.warning("No 'output' field in enhancement response data")
                        return {"error": "No 'output' field in enhancement response data"}
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse output JSON: {e}")
                    return {"error": f"Failed to parse output JSON: {e}"}
            else:
                logger.warning("No output in MayaClient response")
                return {"error": "No output in response"}
        else:
            logger.warning(f"MayaClient failed, falling back to POST: {result['error']}")
            # MayaClient 失败，回退到 POST 方式

    # 使用 POST 方式调用
    logger.info("Using POST request for image quality enhancement")

    url = "https://paiplusinference.alipay.com/inference/c679eb929e2cdd91_main_deeplpf/v1"
    body = {
        "features": {},
        "tensorFeatures": {
            "input": {
                "shapes": [1],
                "stringValues": [json.dumps({
                    "cover_url": cover_url,
                    "cover_id": cover_id
                })]
            }
        }
    }
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    try:
        response = requests.post(url=url, json=body, headers=headers)
        res = response.json()
        logger.info(f"Image quality enhancement response: {res}")

        # 检查API调用是否成功
        if res.get('success') and 'resultMap' in res and 'output' in res['resultMap']:
            output_str = res['resultMap']['output']
            output_data = json.loads(output_str)
            logger.info(f"Parsed output data: {output_data}")

            # 从嵌套的output中提取增强后的图片信息
            if 'output' in output_data:
                enhanced_output = output_data['output']
                return enhanced_output
            else:
                logger.warning("No 'output' field in enhancement response data")
                return {"error": "No 'output' field in enhancement response data"}
        else:
            logger.warning(f"Enhancement API call failed: {res.get('errorMessage', 'Unknown error')}")
            return {"error": res.get('errorMessage', 'Unknown error')}

    except Exception as e:
        logger.error(f"Unexpected error in image quality enhancement: {e}")
        return {"error": str(e)}
